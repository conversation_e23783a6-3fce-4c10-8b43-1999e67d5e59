import { Admin } from "@api/admin/admin.model";
import { appDataSource } from "ormconfig";
import { Repository } from "typeorm";

export class AdminRepository extends Repository<Admin> {

	private static repository: Repository<Admin>;

	static getInstance(): Repository<Admin> {
		if (!AdminRepository.repository) {
			AdminRepository.repository = appDataSource.getRepository(Admin);
		}
		return AdminRepository.repository;
	}
}
