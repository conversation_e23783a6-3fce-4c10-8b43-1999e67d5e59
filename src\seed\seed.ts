import { Admin } from '@api/admin/admin.model';
import { Logger, EncUtil } from 'common';
import { getRepository } from 'typeorm';

export async function seed() {
	try {
		const password = process.env.PASSWORD_ADMIN || 'admin@123';

		// const checkAdmin = await Admin.findOne({
		// 	email: process.env.EMAIL_ADMIN || '<EMAIL>',
		// });
		const userRepository = getRepository(Admin);
		let checkAdmin = await userRepository.findOne({
			where: {
				email: process.env.EMAIL_ADMIN || '<EMAIL>',
			},
		});
		if (!checkAdmin) {
			const newAdmin = new Admin({
				email: process.env.EMAIL_ADMIN || '<EMAIL>',
				password: await EncUtil.createHash(password),
			});
			await newAdmin.save();

			Logger.info('Seeder successfully');
		} else {
			Logger.info('Already seeded');
		}
	} catch (error) {
		Logger.error('Seeder failed!');
		throw error;
	}
}
