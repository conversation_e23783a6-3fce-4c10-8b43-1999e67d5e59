import { DataSource } from 'typeorm';
import env from './src/env';

export const appDataSource = new DataSource({
	type: 'postgres', // hoặc 'mysql', 'sqlite', 'mariadb'
	host: env.database.host,
	port: env.database.port,
	username: env.database.username,
	password: env.database.password,
	database: env.database.name,

	// Entities
	entities: ['./src/entities/*.ts'],

	// Migrations
	migrations: ['./src/migrations/*.ts'],
	migrationsTableName: 'migrations',
	migrationsRun: false,

	// Development settings
	synchronize: env.app.isDevelopment, // Auto create tables in dev
	logging: env.app.isDevelopment,

	// Production settings
	ssl: env.app.isProduction ? { rejectUnauthorized: false } : false,

	// Connection pool
	extra: {
		connectionLimit: 10,
		acquireTimeout: 60000,
		timeout: 60000,
	},
});

export const initializeDataSource = async () => {
	try {
		await appDataSource.initialize();
		console.log('Database connection established successfully');
	} catch (error) {
		console.error('Error during Data Source initialization:', error);
		throw error;
	}
};
